#!/usr/bin/env ruby

# Test script to verify bundle creation with metadata works correctly
# This simulates what the wizard should do

require_relative 'config/environment'

puts "🧪 Testing Bundle Creation with Metadata Fix"
puts "=" * 50

# Set up company context (using the same company ID from the JWT)
company_id = *********
company = Company.find_by(id: company_id) || OpenStruct.new(id: company_id, name: "Test Company")

# Test data - simulating what the wizard would send
test_metadata = {
  bundle_type: 'dynamic_bundle',
  categories: [
    {
      id: 1,
      name: "Wellness",
      products: [
        { id: 62, name: "Can-Am Maverick X3", selected: true },
        { id: 63, name: "Citizen Cabin", selected: false }
      ]
    },
    {
      id: 2, 
      name: "Technology",
      products: [
        { id: 9576, name: "Apple Vision Pro", selected: true }
      ]
    }
  ],
  products: [
    { id: 62, category_id: 1, quantity: 1 },
    { id: 9576, category_id: 2, quantity: 1 }
  ],
  created_via: 'test_script',
  wizard_version: '2.0'
}

puts "📦 Creating bundle with metadata:"
puts "   Name: Test Bundle with Categories"
puts "   SKU: TEST-METADATA-001"
puts "   Categories: #{test_metadata[:categories].length}"
puts "   Products: #{test_metadata[:products].length}"
puts

# Create bundle using the fixed service
result = Fluid::BundlesService.call(
  action: :create_shell,
  name: "Test Bundle with Categories",
  sku: "TEST-METADATA-001",
  description: "Testing bundle creation with proper metadata",
  metadata: test_metadata,
  company: company
)

if result.success?
  bundle_data = result.data[:bundle]
  puts "✅ Bundle created successfully!"
  puts "   ID: #{bundle_data['id']}"
  puts "   Title: #{bundle_data['title']}"
  puts "   SKU: #{bundle_data['sku']}"
  puts "   Metadata categories: #{bundle_data['metadata']['categories'].length rescue 'N/A'}"
  puts "   Metadata products: #{bundle_data['metadata']['products'].length rescue 'N/A'}"
  puts
  puts "🔍 Full metadata:"
  puts bundle_data['metadata'].inspect
else
  puts "❌ Bundle creation failed:"
  puts "   Error: #{result.error}"
end

puts
puts "🧪 Test completed!"
