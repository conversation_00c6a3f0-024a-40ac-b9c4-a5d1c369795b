import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "categoryGrid",
    "productsGrid",
    "productsTitle",
    "dropZone",
    "emptyState",
    "selectedProducts",
    "productCount",
    "productCountBadge",
    "searchInput",
    "saveButton",
    "form",
    "bundleDataInput"
  ]

  static values = {
    availableProducts: Array,
    bundleProducts: Array
  }

  connect() {
    console.log("🏗️ Bundle Builder Controller connected!")

    // Initialize data
    this.bundleProductsValue = this.bundleProductsValue || []
    this.currentCategory = 'supplements'

    // Setup drag and drop
    this.setupDragAndDrop()

    // Load initial products from first category
    this.loadInitialProducts()

    // Update UI
    this.updateUI()
  }

  // Load products from the first active category
  loadInitialProducts() {
    const firstActiveCategory = this.categoryGridTarget.querySelector('.category-card.active')
    if (firstActiveCategory) {
      this.loadProductsFromCategory(firstActiveCategory)
    }
  }

  // Handle category selection
  selectCategory(event) {
    const categoryCard = event.currentTarget

    // Update active category
    this.categoryGridTarget.querySelectorAll('.category-card').forEach(card => {
      card.classList.remove('active')
    })
    categoryCard.classList.add('active')

    // Load products for this category
    this.loadProductsFromCategory(categoryCard)
  }

  // Load products from category data
  loadProductsFromCategory(categoryCard) {
    const categoryId = categoryCard.dataset.categoryId
    const categoryName = categoryCard.dataset.categoryName
    const productsData = categoryCard.dataset.products

    console.log(`Loading products for category: ${categoryName}`)

    try {
      const products = JSON.parse(productsData)

      // Update products title with category name and count
      if (this.hasProductsTitleTarget) {
        this.productsTitleTarget.textContent = `${categoryName} (${products.length})`
      }

      this.renderProducts(products)
    } catch (error) {
      console.error('Error parsing products data:', error)
      this.showProductsError()
    }
  }

  // Render products in the grid
  renderProducts(products) {
    if (!products || products.length === 0) {
      this.showNoProducts()
      return
    }

    const productsHTML = products.map(product => `
      <div class="product-card"
           draggable="true"
           data-product-id="${product.id}"
           data-product-data='${JSON.stringify(product).replace(/'/g, '&apos;')}'
           data-action="dragstart->bundle-builder#handleDragStart dragend->bundle-builder#handleDragEnd click->bundle-builder#addProduct">
        <div class="product-image">
          ${product.image_url ?
            `<img src="${product.image_url}" alt="${product.name || product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">` :
            (product.emoji ? product.emoji : '📦')
          }
        </div>
        <div class="product-info">
          <div class="product-name">${product.name || product.title}</div>
          <div class="product-price">${product.price || '$0.00'}</div>
        </div>
      </div>
    `).join('')

    this.productsGridTarget.innerHTML = productsHTML
  }

  // Show no products message
  showNoProducts() {
    this.productsGridTarget.innerHTML = `
      <div class="text-center py-4 text-muted">
        <div class="mb-2">📦</div>
        <div>No products found in this category</div>
      </div>
    `
  }

  // Show products error
  showProductsError() {
    this.productsGridTarget.innerHTML = `
      <div class="col-12">
        <div class="text-center py-4 text-danger">
          <div class="mb-2">⚠️</div>
          <div>Error loading products</div>
        </div>
      </div>
    `
  }

  // Debug method to add a test product
  debugAddProduct() {
    const testProduct = {
      id: Date.now(),
      title: "Test Product",
      price: "19.99",
      image_url: null
    }

    console.log("🧪 Adding test product:", testProduct)
    this.addProductToBundle(testProduct)
  }

  // Setup drag and drop functionality
  setupDragAndDrop() {
    if (this.hasDropZoneTarget) {
      this.dropZoneTarget.addEventListener('dragover', this.handleDragOver.bind(this))
      this.dropZoneTarget.addEventListener('dragleave', this.handleDragLeave.bind(this))
      this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this))
    }
  }

  // Handle drag start
  handleDragStart(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      event.dataTransfer.setData('text/plain', productData)
      event.dataTransfer.effectAllowed = 'copy'
      event.currentTarget.classList.add('dragging')
      console.log("🎯 Drag started for product:", JSON.parse(productData).title)
    }
  }

  // Handle drag end
  handleDragEnd(event) {
    event.currentTarget.classList.remove('dragging')
  }

  // Handle drag over
  handleDragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
    this.dropZoneTarget.classList.add('drag-over')
  }

  // Handle drag leave
  handleDragLeave(event) {
    // Only remove drag-over if we're actually leaving the drop zone
    if (!this.dropZoneTarget.contains(event.relatedTarget)) {
      this.dropZoneTarget.classList.remove('drag-over')
    }
  }

  // Handle drop
  handleDrop(event) {
    event.preventDefault()
    this.dropZoneTarget.classList.remove('drag-over')

    try {
      const productDataString = event.dataTransfer.getData('text/plain')
      console.log("📦 Raw product data:", productDataString)

      if (productDataString) {
        const productData = JSON.parse(productDataString)
        console.log("📦 Product dropped:", productData)
        this.addProductToBundle(productData)
      } else {
        console.warn("No product data found in drop event")
      }
    } catch (error) {
      console.error('Error handling drop:', error)
    }
  }

  // Add product to bundle (via click or drag)
  addProduct(event) {
    const productData = event.currentTarget.dataset.productData

    if (productData) {
      try {
        const product = JSON.parse(productData)
        console.log("👆 Product clicked:", product)
        this.addProductToBundle(product)
      } catch (error) {
        console.error('Error parsing product data:', error)
      }
    }
  }

  // Add product to bundle
  addProductToBundle(product) {
    console.log('🔍 Checking if product exists in bundle:', product)
    console.log('🔍 Current bundle products:', this.bundleProductsValue)

    // Check if product already exists
    const existingProduct = this.bundleProductsValue.find(p => p.id.toString() === product.id.toString())
    if (existingProduct) {
      console.log('⚠️ Product already in bundle:', existingProduct)
      return
    }

    // Add to bundle
    this.bundleProductsValue = [...this.bundleProductsValue, product]
    console.log(`✅ Added ${product.name || product.title} to bundle. New count: ${this.bundleProductsValue.length}`)

    // Update UI
    this.updateUI()
  }

  // Remove product from bundle
  removeProduct(event) {
    const productId = event.currentTarget.dataset.productId
    this.bundleProductsValue = this.bundleProductsValue.filter(
      p => p.id.toString() !== productId
    )

    console.log(`Removed product ${productId} from bundle`)
    this.updateUI()
  }

  // Clear all products from bundle
  clearBundle() {
    if (this.bundleProductsValue.length === 0) return

    if (confirm('Are you sure you want to clear all products from the bundle?')) {
      this.bundleProductsValue = []
      this.updateUI()
    }
  }

  // Update UI based on current state
  updateUI() {
    this.updateProductCount()
    this.updateSelectedProducts()
    this.updateSaveButton()
    this.updateFormData()
  }

  // Update product count displays
  updateProductCount() {
    const count = this.bundleProductsValue.length
    const text = `${count} product${count !== 1 ? 's' : ''}`

    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = text
    }

    if (this.hasProductCountBadgeTarget) {
      this.productCountBadgeTarget.textContent = text
    }
  }

  // Update selected products display
  updateSelectedProducts() {
    const hasProducts = this.bundleProductsValue.length > 0

    console.log(`🔄 Updating UI - Has products: ${hasProducts}, Count: ${this.bundleProductsValue.length}`)

    // Show/hide empty state
    if (this.hasEmptyStateTarget) {
      this.emptyStateTarget.style.display = hasProducts ? 'none' : 'flex'
      console.log(`👁️ Empty state display: ${this.emptyStateTarget.style.display}`)
    }

    // Show/hide selected products
    if (this.hasSelectedProductsTarget) {
      this.selectedProductsTarget.style.display = hasProducts ? 'block' : 'none'
      console.log(`👁️ Selected products display: ${this.selectedProductsTarget.style.display}`)

      if (hasProducts) {
        const productsHTML = this.renderSelectedProducts()
        this.selectedProductsTarget.innerHTML = productsHTML
        console.log(`📦 Rendered ${this.bundleProductsValue.length} products`)
      } else {
        this.selectedProductsTarget.innerHTML = ''
      }
    }
  }

  // Render selected products
  renderSelectedProducts() {
    return this.bundleProductsValue.map(product => `
      <div class="bundle-product-card">
        <div class="bundle-product-image">
          ${product.image_url ?
            `<img src="${product.image_url}" alt="${product.name || product.title}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">` :
            `<span style="font-size: 1.5rem;">${product.emoji || '📦'}</span>`
          }
        </div>
        <div class="bundle-product-info">
          <div class="bundle-product-name">${product.name || product.title}</div>
          <div class="bundle-product-price">${product.price || '$0.00'}</div>
        </div>
        <button type="button"
                class="bundle-product-remove"
                data-product-id="${product.id}"
                data-action="click->bundle-builder#removeProduct"
                title="Remove product">
          ×
        </button>
      </div>
    `).join('')
  }

  // Update save button state
  updateSaveButton() {
    if (this.hasSaveButtonTarget) {
      this.saveButtonTarget.disabled = this.bundleProductsValue.length === 0
    }
  }

  // Update form data for submission
  updateFormData() {
    if (this.hasBundleDataInputTarget) {
      const bundleData = {
        products: this.bundleProductsValue.map(product => ({
          id: product.id,
          title: product.title,
          price: product.price,
          sku: product.sku
        }))
      }

      this.bundleDataInputTarget.value = JSON.stringify(bundleData)
    }
  }
}
