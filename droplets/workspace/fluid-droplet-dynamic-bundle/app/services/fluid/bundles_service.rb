# frozen_string_literal: true

module Fluid
  # Service for interacting with Fluid Bundles API
  # Handles creation and management of bundle shells for dynamic bundles
  #
  # @example Create bundle shell
  #   result = BundlesService.call(
  #     action: :create_shell,
  #     name: "Transformation Bundle",
  #     sku: "TRANS-BUNDLE-001",
  #     description: "Customizable wellness transformation bundle"
  #   )
  #
  # @example Add products to bundle
  #   result = BundlesService.call(
  #     action: :add_products,
  #     bundle_id: "bundle-123",
  #     products: [
  #       { product_id: "prod-1", variant_id: "var-1", quantity: 2 },
  #       { product_id: "prod-2", variant_id: "var-2", quantity: 1 }
  #     ]
  #   )
  #
  # @example Update bundle
  #   result = BundlesService.call(
  #     action: :update,
  #     bundle_id: "bundle-123",
  #     name: "Updated Bundle Name"
  #   )
  #
  class BundlesService < ApplicationService
    # Initialize the service with action and parameters
    # @param action [Symbol] the action to perform (:create_shell, :find, :update, :add_products, :remove_products, :list)
    # @param bundle_id [String] bundle ID (for :find, :update, :add_products, :remove_products actions)
    # @param name [String] bundle name (for :create_shell, :update actions)
    # @param sku [String] bundle SKU (for :create_shell action)
    # @param description [String] bundle description (for :create_shell, :update actions)
    # @param metadata [Hash] bundle metadata (for :update actions)
    # @param products [Array] array of product hashes (for :add_products, :remove_products actions)
    # @param page [Integer] page number for pagination (default: 1)
    # @param per_page [Integer] items per page (default: 20)
    def initialize(action:, bundle_id: nil, name: nil, sku: nil, description: nil, metadata: nil, products: [], page: 1, per_page: 20, company: nil)
      @action = action.to_sym
      @bundle_id = bundle_id
      @name = name
      @sku = sku
      @description = description
      @metadata = metadata
      @products = products || []
      @page = page
      @per_page = per_page
      @company = company
    end

    # Execute the bundles service action
    # @return [ServiceResult] the result of the operation
    def call
      case @action
      when :create_shell
        create_bundle_shell
      when :find
        find_bundle
      when :update
        update_bundle
      when :add_products
        add_products_to_bundle
      when :remove_products
        remove_products_from_bundle
      when :list
        list_bundles
      else
        failure("Invalid action: #{@action}. Supported actions: :create_shell, :find, :update, :add_products, :remove_products, :list")
      end
    end

    private



    # Create a new bundle shell (empty bundle for dynamic configuration)
    # @return [ServiceResult] created bundle
    def create_bundle_shell
      return failure("Bundle name is required") if @name.blank?
      return failure("Bundle SKU is required") if @sku.blank?

      Rails.logger.info("Creating bundle shell: #{@name} (#{@sku})")

      begin
        # Use FluidClient for reliable API calls
        client = FluidClient.new
        response = client.post("/api/company/v1/products", body: create_shell_params)

        # Transform response to standardized format
        transformed_data = transform_bundle_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("Failed to create bundle shell: #{e.message}")
        failure("Failed to create bundle: #{e.message}")
      end
    end

    # Find a specific bundle by ID
    # @return [ServiceResult] bundle details
    def find_bundle
      return failure("Bundle ID is required") if @bundle_id.blank?

      Rails.logger.info("Finding bundle with ID: #{@bundle_id}")

      # Find bundle as a product with bundle metadata
      result = get("/api/company/v1/products/#{@bundle_id}")
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_bundle_response(result.data)
      success(transformed_data)
    end

    # Update an existing bundle
    # @return [ServiceResult] updated bundle
    def update_bundle
      return failure("Bundle ID is required") if @bundle_id.blank?

      Rails.logger.info("Updating bundle ID: #{@bundle_id}")

      # Update bundle as a product with bundle metadata
      result = put("/api/company/v1/products/#{@bundle_id}", body: update_params)
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_bundle_response(result.data)
      success(transformed_data)
    end

    # Add products to a bundle
    # @return [ServiceResult] updated bundle with products
    def add_products_to_bundle
      return failure("Bundle ID is required") if @bundle_id.blank?
      return failure("Products array is required") if @products.empty?

      Rails.logger.info("Adding #{@products.length} products to bundle ID: #{@bundle_id}")
      
      result = post("/api/company/v1/bundles/#{@bundle_id}/products", body: { products: @products })
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_bundle_response(result.data)
      success(transformed_data)
    end

    # Remove products from a bundle
    # @return [ServiceResult] updated bundle without products
    def remove_products_from_bundle
      return failure("Bundle ID is required") if @bundle_id.blank?
      return failure("Products array is required") if @products.empty?

      Rails.logger.info("Removing #{@products.length} products from bundle ID: #{@bundle_id}")
      
      result = delete("/api/company/v1/bundles/#{@bundle_id}/products", body: { products: @products })
      
      return result if result.failure?
      
      # Transform response to standardized format
      transformed_data = transform_bundle_response(result.data)
      success(transformed_data)
    end

    # List all bundles with pagination
    # @return [ServiceResult] paginated bundles list
    def list_bundles
      Rails.logger.info("Listing bundles - page: #{@page}, per_page: #{@per_page}")

      begin
        # Use FluidClient for reliable API calls
        client = FluidClient.new
        response = client.get("/api/company/v1/products", query: list_params)

        # Transform response to standardized format
        transformed_data = transform_bundles_response(response)
        success(transformed_data)
      rescue => e
        Rails.logger.error("Failed to list bundles: #{e.message}")
        failure("Failed to load bundles: #{e.message}")
      end
    end

    # Build create shell parameters for product API
    # @return [Hash] create shell parameters
    def create_shell_params
      # Default metadata structure
      default_metadata = {
        bundle_type: "dynamic_bundle",
        created_by: "dynamic_bundle_droplet",
        categories: [],
        configuration: {}
      }

      # Merge with provided metadata if available
      final_metadata = if @metadata.present?
        default_metadata.merge(@metadata)
      else
        default_metadata
      end

      {
        product: {
          title: @name,
          name: @name,
          sku: @sku,
          description: @description,
          status: "active",
          product_type: "bundle",
          metadata: final_metadata
        }
      }
    end

    # Build update parameters for product API
    # @return [Hash] update parameters
    def update_params
      product_params = {}
      product_params[:name] = @name if @name.present?
      product_params[:description] = @description if @description.present?

      if @metadata.present?
        product_params[:metadata] = @metadata
      end

      { product: product_params }
    end

    # Build list parameters for products API
    # @return [Hash] list parameters
    def list_params
      {
        page: @page,
        per_page: @per_page,
        product_type: "bundle",
        status: ["active"]
      }
    end

    # Transform bundle API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_bundle_response(response_data)
      {
        bundle: response_data.dig("data") || response_data
      }
    end

    # Transform bundles API response to standardized format
    # @param response_data [Hash] raw API response
    # @return [Hash] transformed data
    def transform_bundles_response(response_data)
      {
        bundles: response_data.dig("data") || [],
        pagination: {
          current_page: response_data.dig("meta", "current_page") || @page,
          per_page: response_data.dig("meta", "per_page") || @per_page,
          total_pages: response_data.dig("meta", "total_pages") || 1,
          total_count: response_data.dig("meta", "total_count") || 0
        }
      }
    end
  end
end
