<% content_for :title, "Bundle Creation Wizard - Step #{@step_number}" %>

<!-- Hide main header for cleaner wizard experience -->
<style>
header {
  display: none !important;
}
</style>

<!-- Bundle Wizard with Stimulus Controller -->
<div class="wizard-container"
     data-controller="bundle-wizard"
     data-bundle-wizard-current-step-value="<%= @current_step %>"
     data-bundle-wizard-total-steps-value="3"
     data-bundle-wizard-wizard-state-value="<%= @wizard.state %>">

  <!-- Progress Bar -->
  <div class="wizard-progress mb-4">
    <div class="progress-steps">
      <% wizard_steps = ['info', 'builder', 'preview'] %>
      <% step_labels = { 'info' => 'Bundle Info', 'builder' => 'Configure Products', 'preview' => 'Review & Save' } %>
      <% wizard_steps.each_with_index do |step, index| %>
        <%
          step_position = index + 1
          # A step is completed if we're past it
          is_completed = step_position < @step_number
          # A step is current if it matches the current step
          is_current = step == @current_step
          # A step is active if it's completed or current
          is_active = is_completed || is_current
        %>
        <div class="step <%= 'completed' if is_completed %> <%= 'active' if is_active %> <%= 'current' if is_current %>">
          <div class="step-number"><%= index + 1 %></div>
          <div class="step-label"><%= step_labels[step] || step.humanize %></div>
        </div>
        <% unless index == wizard_steps.length - 1 %>
          <div class="step-connector <%= 'completed' if step_position < @step_number %>"></div>
        <% end %>
      <% end %>
    </div>
  </div>

  <!-- Step Content with Turbo Frame -->
  <%= turbo_frame_tag "wizard_step", data: { bundle_wizard_target: "step" } do %>
    <div class="wizard-content">
      <% case @current_step %>
      <% when 'info' %>
        <%= render 'info_step' %>
      <% when 'builder' %>
        <%= render 'builder_step' %>
      <% when 'preview' %>
        <%= render 'preview_step' %>
      <% end %>
    </div>
  <% end %>
</div>

<style>
.wizard-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 10px 20px;
}

.wizard-progress {
  background: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  margin-bottom: 15px;
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 120px;
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 13px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.step.completed .step-number {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.step.active .step-number {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.step.current .step-number {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.25);
  animation: pulse-current 2s infinite;
}

@keyframes pulse-current {
  0% {
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.25);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(52, 152, 219, 0.15);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.25);
  }
}

.step-label {
  font-size: 11px;
  font-weight: 500;
  color: #6c757d;
  transition: color 0.3s ease;
}

.step.completed .step-label {
  color: #27ae60;
  font-weight: 600;
}

.step.active .step-label {
  color: #27ae60;
  font-weight: 600;
}

.step.current .step-label {
  color: #3498db;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(52, 152, 219, 0.2);
}

.step-connector {
  flex: 1;
  height: 3px;
  background: #e9ecef;
  margin: 0 15px;
  margin-bottom: 28px;
  transition: all 0.3s ease;
  border-radius: 2px;
  position: relative;
}

.step-connector.completed {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  box-shadow: 0 1px 3px rgba(39, 174, 96, 0.3);
}

.step-connector.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.wizard-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.step-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.step-header h2 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.step-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.step-body {
  padding: 40px;
}

.wizard-actions {
  background: #f8f9fa;
  padding: 20px 40px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wizard-actions .btn {
  min-width: 120px;
}

.bundle-info {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 30px;
}

.bundle-info h5 {
  margin: 0 0 8px 0;
  color: #1976d2;
  font-size: 16px;
}

.bundle-info p {
  margin: 0;
  color: #424242;
  font-size: 14px;
}

@media (max-width: 768px) {
  .wizard-container {
    padding: 10px;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 20px;
  }
  
  .step-connector {
    display: none;
  }
  
  .step {
    min-width: auto;
  }
  
  .wizard-actions {
    flex-direction: column;
    gap: 15px;
  }
  
  .wizard-actions .btn {
    width: 100%;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Update contextual header for wizard
  if (window.updateContextualHeader) {
    const bundleName = '<%= j @wizard.name %>';
    const stepNumber = <%= @step_number %>;
    const totalSteps = <%= @total_steps %>;
    
    window.updateContextualHeader(
      `🧙‍♂️ Bundle Creation Wizard - Step ${stepNumber}/${totalSteps}`,
      [
        {
          text: '📋 Back to Bundles',
          class: 'btn-outline-secondary',
          onclick: 'backToBundlesList()'
        }
      ]
    );
  }
});

// Global function for header action
window.backToBundlesList = function() {
  if (confirm('Are you sure you want to leave the wizard? Your progress will be lost.')) {
    window.location.href = '<%= admin_bundles_path %>';
  }
};
</script>
