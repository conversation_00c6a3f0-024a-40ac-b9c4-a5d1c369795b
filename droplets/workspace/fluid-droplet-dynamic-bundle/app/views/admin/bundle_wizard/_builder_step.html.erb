<!-- Step 2: Bundle Builder -->
<div class="step-content">

  <form id="builder-form"
        action="<%= process_bundle_wizard_step_admin_bundles_path(step: 'builder') %>"
        method="post"
        accept-charset="UTF-8"
        data-turbo-frame="wizard_step"
        data-bundle-wizard-target="form"
        data-controller="bundle-builder"
        data-bundle-builder-selected-category-value=""
        data-bundle-builder-bundle-products-value="[]"
        data-bundle-builder-available-products-value="<%= (@categories&.flat_map { |c| c['products'] } || []).to_json.html_safe %>"
    <%= hidden_field_tag :authenticity_token, form_authenticity_token %>
    <%= hidden_field_tag :_method, 'post' %>
    <%= hidden_field_tag :builder_config, '{}', id: 'builder_config_input' %>





    <!-- Main Builder Interface -->
    <div class="row g-3 mb-4 builder-interface">
      <!-- Left Column - Store Catalog -->
      <div class="col-6" style="display: flex; flex-direction: column;">

        <!-- Categories Section (Fixed Height with Scrolling) -->
        <div style="flex: 0 0 auto; display: flex; flex-direction: column;">
          <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-light border-0 py-2">
              <h6 class="mb-0">
                <span class="me-2">🏪</span>Store Categories
                <% if @categories.present? %>
                  <small class="text-muted">(<%= @categories.length %>)</small>
                <% end %>
              </h6>
            </div>
            <div class="card-body p-2 categories-scroll-container">
              <div class="row g-2" data-bundle-builder-target="categoryGrid">
                <% if @categories.present? %>
                  <% @categories.each_with_index do |category, index| %>
                    <div class="col-6">
                      <div class="category-card <%= 'active' if index == 0 %>"
                           data-category-id="<%= category['id'] %>"
                           data-category-name="<%= category['name'] %>"
                           data-products='<%= category['products'].to_json %>'
                           data-action="click->bundle-builder#selectCategory"
                           style="cursor: pointer; user-select: none; transition: all 0.2s ease;">
                        <div class="category-icon mb-1">📦</div>
                        <div class="category-name"><%= category['name'] %></div>
                        <small class="text-muted">
                          <%= category['products']&.length || 0 %> items
                        </small>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <div class="col-12">
                    <div class="text-center py-3 text-muted">
                      <div class="mb-2">📦</div>
                      <div>No categories found</div>
                      <small>Check your Fluid API connection</small>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Products Section (Fixed Height) -->
        <div style="flex: 0 0 auto; display: flex; flex-direction: column; margin-top: 0.75rem;">
          <div class="card border-0 shadow-sm" style="height: 400px;">
            <div class="card-header bg-light border-0 py-2">
              <h6 class="mb-0">
                <span class="me-2">📦</span>Products
                <small class="text-muted" data-bundle-builder-target="productsTitle">Select a category</small>
              </h6>
            </div>
            <div class="card-body p-2 products-scroll-container">
              <div class="products-list" data-bundle-builder-target="productsGrid">
                <!-- Products will be loaded here by Stimulus -->
                <div class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading products...</span>
                  </div>
                  <p class="text-muted mt-2 mb-0">Loading products...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Bundle Drop Zone -->
      <div class="col-6" style="display: flex; flex-direction: column;">

        <div class="card border-0 shadow-sm h-100">
          <div class="card-header bg-light border-0 py-2">
            <h6 class="mb-0">
              <span class="me-2">📦</span>Bundle Products
              <small class="text-muted">(Drop products here)</small>
            </h6>
          </div>
          <div class="card-body p-2" style="overflow-y: auto;">
            <div class="bundle-drop-zone h-100"
                 id="bundleDropZone"
                 data-bundle-builder-target="dropZone">

              <!-- Empty State -->
              <div class="drop-zone-empty text-center d-flex flex-column justify-content-center h-100"
                   id="dropZoneEmpty"
                   data-bundle-builder-target="emptyState">
                <div class="drop-zone-icon mb-3">📦</div>
                <h6 class="text-muted mb-2">Drop products here to add to bundle</h6>
                <p class="text-muted small mb-0">Drag products from the left panel</p>
              </div>

              <!-- TEST CONTROLLER BUTTON -->
              <div data-controller="test">
                <button type="button"
                        data-action="click->test#testMethod"
                        class="btn btn-info btn-sm mb-2">
                  🧪 TEST: Simple Controller
                </button>
              </div>

              <!-- DEBUG BUTTON -->
              <button type="button"
                      data-action="click->bundle-builder#debugAddProduct"
                      class="btn btn-warning btn-sm mb-2">
                🧪 DEBUG: Add Test Product
              </button>

              <!-- Selected Products -->
              <div class="selected-products-container"
                   id="selectedProductsGrid"
                   data-bundle-builder-target="selectedProducts">
                <div class="row g-1">
                  <!-- Selected products will appear here -->
                </div>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>

  </form>

</div>

<!-- Fixed Navigation Bar -->
<div class="wizard-navigation-bar">
  <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center">
      <!-- Left side - Step indicator -->
      <div class="step-indicator">
        <small class="text-muted">Step 2 of 3</small>
        <div class="step-title">Configure Products</div>
      </div>

      <!-- Center - Bundle summary -->
      <div class="bundle-summary" data-bundle-builder-target="bundleSummary">
        <span class="bundle-count" data-bundle-builder-target="productCountBadge">0</span>
        <small class="text-muted ms-1">products selected</small>
      </div>

      <!-- Right side - Navigation buttons -->
      <div class="navigation-buttons">
        <button type="button"
                class="btn btn-outline-secondary me-3"
                data-bundle-wizard-target="prevButton"
                data-action="click->bundle-wizard#prevStep">
          <span class="me-1">←</span>Cancel
        </button>

        <button type="button"
                class="btn btn-primary"
                data-bundle-wizard-target="nextButton"
                data-action="click->bundle-wizard#nextStep"
                disabled>
          Continue to Preview <span class="ms-1">→</span>
        </button>
      </div>
    </div>
  </div>

</div>

<style>
/* Builder Interface Layout */
.builder-interface {
  height: calc(100vh - 400px); /* Increased to account for fixed navigation bar */
  min-height: 450px;
  max-height: 600px;
  margin-bottom: 80px; /* Space for fixed navigation bar */
}

/* Fixed Navigation Bar */
.wizard-navigation-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #dee2e6;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding: 16px 0;
  z-index: 1050;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.step-title {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.bundle-summary {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #dee2e6;
}

.bundle-count {
  font-weight: 600;
  color: #007bff;
  font-size: 1.1rem;
}

.navigation-buttons {
  display: flex;
  align-items: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .wizard-navigation-bar .d-flex {
    flex-direction: column;
    gap: 12px;
  }

  .step-indicator,
  .bundle-summary,
  .navigation-buttons {
    width: 100%;
    justify-content: center;
    text-align: center;
  }

  .step-indicator {
    align-items: center;
  }

  .wizard-navigation-bar {
    padding: 20px 0;
  }

  .builder-interface {
    margin-bottom: 120px; /* More space for mobile navigation */
  }
}

/* Scroll Containers */
.categories-scroll-container {
  overflow-y: auto;
  max-height: 200px;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.categories-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.categories-scroll-container::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.categories-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.categories-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.products-scroll-container {
  overflow-y: auto;
  height: 320px;
  max-height: 320px;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.products-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 0.75rem;
  padding: 0.5rem;
}

.products-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.products-scroll-container::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.products-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.products-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Category Cards */
.category-card {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 85px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.category-card:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-color: #adb5bd;
}

.category-card.active {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #2196f3;
  box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);
  transform: translateY(-1px);
}

.category-card.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #1976d2);
}

.category-icon {
  font-size: 1.4rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.category-card.active .category-icon {
  opacity: 1;
  transform: scale(1.1);
}

.category-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.125rem;
  line-height: 1.2;
}

.category-card.active .category-name {
  color: #1976d2;
  font-weight: 700;
}

/* Bundle Drop Zone */
.bundle-drop-zone {
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  transition: all 0.3s ease;
  min-height: 300px;
  overflow-y: auto;
}

.bundle-drop-zone.drag-over {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
  border-style: solid;
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
}

.drop-zone-icon {
  font-size: 3rem;
  opacity: 0.4;
  color: #6c757d;
  margin-bottom: 1rem;
}

.bundle-drop-zone.drag-over .drop-zone-icon {
  opacity: 0.8;
  color: #28a745;
  transform: scale(1.1);
}

.selected-products-container {
  padding: 1rem;
}

.selected-products-container:empty + .drop-zone-empty {
  display: flex;
}

.selected-products-container:not(:empty) + .drop-zone-empty {
  display: none;
}



/* Product Cards */
.product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: grab;
  transition: all 0.3s ease;
  height: 60px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  min-width: 260px;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #28a745, #20c997);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.12);
  transform: translateY(-3px);
  border-color: #28a745;
}

.product-card:hover::before {
  transform: scaleX(1);
}

.product-card:active {
  cursor: grabbing;
  transform: translateY(-1px) scale(0.98);
}

.product-card.dragging {
  opacity: 0.6;
  transform: rotate(3deg) scale(0.95);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.product-image {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  object-fit: cover;
  border: 1px solid #e9ecef;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
}

.product-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: #495057;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 0.75rem;
}

.product-price {
  color: #28a745;
  font-weight: 700;
  font-size: 0.8rem;
  background: #f8fff8;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  border: 1px solid #d4edda;
  flex-shrink: 0;
}

/* Bundle Product Cards (in drop zone) */
.bundle-product-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.bundle-product-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #28a745, #20c997);
}

.bundle-product-card:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  border-color: #28a745;
}

.bundle-product-image {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
  border: 1px solid #e9ecef;
}

.bundle-product-info {
  flex: 1;
  min-width: 0;
}

.bundle-product-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.bundle-product-price {
  color: #28a745;
  font-weight: 700;
  font-size: 0.8rem;
  background: #f8fff8;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  border: 1px solid #d4edda;
  display: inline-block;
}

.bundle-product-remove {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  color: #e53e3e;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bundle-product-remove:hover {
  background: #fed7d7;
  color: #c53030;
  transform: scale(1.1);
}
</style>

<!-- All JavaScript functionality moved to Stimulus controllers -->
