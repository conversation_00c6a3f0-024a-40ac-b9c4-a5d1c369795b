<% content_for :title, "Dynamic Bundles" %>

<% if @total_count > 0 %>
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>Bundles</h2>
    <%= link_to new_admin_bundle_path, class: "btn btn-primary" do %>
      Create New Bundle
    <% end %>
  </div>
<% end %>

<% if @total_count > 0 %>
  <p class="text-muted">
    Showing <%= @bundles.length %> of <%= @total_count %> bundles
    <% if @pagination[:current_page] && @pagination[:total_pages] %>
      (Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>)
    <% end %>
  </p>

  <table class="table">
    <thead>
      <tr>
        <th>Name</th>
        <th>SKU</th>
        <th>Description</th>
        <th>Status</th>
        <th>Categories</th>
        <th>Created</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% @bundles.each do |bundle| %>
        <tr>
          <td>
            <strong><%= bundle['name'] %></strong>
          </td>
          <td>
            <code><%= bundle['sku'] %></code>
          </td>
          <td>
            <%= truncate(bundle['description'], length: 60) %>
          </td>
          <td>
            <% if bundle['status'] == 'active' %>
              <span class="badge badge-success">Active</span>
            <% else %>
              <span class="badge badge-secondary">Inactive</span>
            <% end %>
          </td>
          <td>
            <% categories_count = bundle.dig('metadata', 'categories')&.length || 0 %>
            <%= categories_count %> categories
          </td>
          <td>
            <% if bundle['created_at'] %>
              <%= time_ago_in_words(Time.parse(bundle['created_at'])) %> ago
            <% else %>
              -
            <% end %>
          </td>
          <td>
            <div class="action-icons">
              <%= link_to admin_bundle_path(bundle['id']),
                  class: "action-icon view-icon",
                  title: "View bundle details" do %>
                👁️
              <% end %>

              <%= link_to edit_admin_bundle_path(bundle['id']),
                  class: "action-icon edit-icon",
                  title: "Edit bundle" do %>
                ✏️
              <% end %>

              <button type="button"
                      class="action-icon builder-icon"
                      title="Bundle Builder"
                      data-bs-toggle="modal"
                      data-bs-target="#bundleBuilderModal"
                      data-bundle-id="<%= bundle['id'] %>"
                      data-bundle-name="<%= bundle['name'] %>">
                🏗️
              </button>

              <% if bundle['status'] == 'active' %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon deactivate-icon",
                    title: "Deactivate bundle",
                    data: {
                      confirm: "Are you sure you want to deactivate this bundle?",
                      turbo_method: :patch
                    } do %>
                  ⏸️
                <% end %>
              <% else %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon activate-icon",
                    title: "Activate bundle",
                    data: { turbo_method: :patch } do %>
                  ▶️
                <% end %>
              <% end %>

              <%= link_to admin_bundle_path(bundle['id']),
                  method: :delete,
                  class: "action-icon delete-icon",
                  title: "Delete bundle",
                  data: {
                    confirm: "Are you sure you want to delete this bundle? This action cannot be undone.",
                    turbo_method: :delete
                  } do %>
                🗑️
              <% end %>
            </div>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <!-- Pagination -->
  <% if @pagination[:total_pages] && @pagination[:total_pages] > 1 %>
    <nav class="mt-4">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <% if @pagination[:current_page] > 1 %>
            <%= link_to "← Previous", admin_bundles_path(page: @pagination[:current_page] - 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
        
        <div>
          Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>
        </div>
        
        <div>
          <% if @pagination[:current_page] < @pagination[:total_pages] %>
            <%= link_to "Next →", admin_bundles_path(page: @pagination[:current_page] + 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
      </div>
    </nav>
  <% end %>

<% else %>
  <!-- Empty State -->
  <div class="text-center" style="padding: 40px 20px; margin-top: 60px;">
    <div style="font-size: 72px; color: #bdc3c7; margin-bottom: 32px;">📦</div>
    <h2 style="color: #2c3e50; font-weight: 600; margin-bottom: 20px; font-size: 28px;">Ready to get started?</h2>
    <p style="color: #7f8c8d; font-size: 18px; margin-bottom: 40px; max-width: 450px; margin-left: auto; margin-right: auto; line-height: 1.5;">
      Create your first bundle to begin building configurable product bundles.
    </p>
    <%= link_to new_admin_bundle_path, class: "btn btn-primary", style: "padding: 14px 28px; font-size: 16px; font-weight: 600;" do %>
      Create Your First Bundle
    <% end %>
  </div>
<% end %>

<% content_for :modals do %>
<!-- Bundle Creation Modal -->
<div class="modal fade" id="createBundleModal" tabindex="-1" aria-labelledby="createBundleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createBundleModalLabel">
          <span class="me-2">📦</span>Create New Dynamic Bundle
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <%= form_with url: admin_bundles_path, method: :post, local: true, id: "createBundleForm", class: "needs-validation", novalidate: true, scope: :bundle do |form| %>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <%= form.label :name, "Bundle Name", class: "form-label" %>
                <%= form.text_field :name, class: "form-control", placeholder: "e.g., Yoli Transformation Bundle", required: true %>
                <div class="invalid-feedback">
                  Please provide a bundle name.
                </div>
                <div class="form-text">
                  This will be the display name for your bundle
                </div>
              </div>

              <div class="mb-3">
                <%= form.label :sku, "SKU", class: "form-label" %>
                <%= form.text_field :sku, class: "form-control", placeholder: "e.g., YOLI-TRANS-001", required: true %>
                <div class="invalid-feedback">
                  Please provide a unique SKU.
                </div>
                <div class="form-text">
                  Unique identifier for this bundle
                </div>
              </div>

              <div class="mb-3">
                <%= form.label :description, "Description", class: "form-label" %>
                <%= form.text_area :description, class: "form-control", rows: 3, placeholder: "Describe what this bundle offers..." %>
                <div class="form-text">
                  Optional description for customers
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="bundle-preview-card">
                <h6 class="mb-3">Bundle Preview</h6>
                <div class="preview-content">
                  <div class="preview-name">Bundle Name</div>
                  <div class="preview-sku">SKU</div>
                  <div class="preview-description">Description will appear here...</div>
                  <div class="preview-categories">
                    <small class="text-muted">Categories: 0</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">
            Continue to Builder <span class="ms-1">→</span>
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>
<% end %>

<style>
  .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
  }
  
  .badge-success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  .badge-warning {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .action-icons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
  }

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;
  }

  .action-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .view-icon {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }

  .view-icon:hover {
    background-color: #e9ecef;
    border-color: #6c757d;
  }

  .edit-icon {
    background-color: #e3f2fd;
    border-color: #bbdefb;
  }

  .edit-icon:hover {
    background-color: #bbdefb;
    border-color: #2196f3;
  }

  .activate-icon {
    background-color: #e8f5e8;
    border-color: #c8e6c9;
  }

  .activate-icon:hover {
    background-color: #c8e6c9;
    border-color: #4caf50;
  }

  .deactivate-icon {
    background-color: #fff3e0;
    border-color: #ffcc02;
  }

  .deactivate-icon:hover {
    background-color: #ffcc02;
    border-color: #ff9800;
  }

  .delete-icon {
    background-color: #ffebee;
    border-color: #ffcdd2;
  }

  .delete-icon:hover {
    background-color: #ffcdd2;
    border-color: #f44336;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }

  /* Bundle Creation Modal Styles */
  .bundle-preview-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    height: fit-content;
  }

  .preview-content {
    font-size: 14px;
  }

  .preview-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    background-color: #fff3cd;
    padding: 4px 8px;
    border-radius: 4px;
    border: 2px solid #ffc107;
  }

  .preview-sku {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 8px;
  }

  .preview-description {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    min-height: 40px;
  }

  .preview-categories {
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
  }

  .modal-header {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .modal-header .btn-close {
    filter: invert(1);
  }

  .modal-title {
    font-weight: 600;
  }

  .form-label {
    font-weight: 500;
    color: #495057;
  }

  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Ensure Bootstrap is loaded
  if (typeof bootstrap === 'undefined') {
    console.warn('Bootstrap not loaded, using fallback modal functionality');
  }

  // Bundle Creation Modal - Live Preview
  const modal = document.getElementById('createBundleModal');
  const nameInput = document.querySelector('#createBundleForm input[name="bundle[name]"]');
  const skuInput = document.querySelector('#createBundleForm input[name="bundle[sku]"]');
  const descriptionInput = document.querySelector('#createBundleForm textarea[name="bundle[description]"]');

  const previewName = document.querySelector('.preview-name');
  const previewSku = document.querySelector('.preview-sku');
  const previewDescription = document.querySelector('.preview-description');



  function updatePreview() {
    if (nameInput && previewName) {
      previewName.textContent = nameInput.value || 'Bundle Name';
    }
    if (skuInput && previewSku) {
      previewSku.textContent = skuInput.value || 'SKU';
    }
    if (descriptionInput && previewDescription) {
      previewDescription.textContent = descriptionInput.value || 'Description will appear here...';
    }
  }

  // Auto-generate SKU from name
  function generateSku(name) {
    return name
      .toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 20);
  }

  if (nameInput) {
    nameInput.addEventListener('input', function() {
      updatePreview();

      // Auto-generate SKU if it's empty
      if (skuInput && !skuInput.value) {
        const generatedSku = generateSku(this.value);
        if (generatedSku && generatedSku.length > 0) {
          const finalSku = generatedSku + '-001';
          skuInput.value = finalSku;
          updatePreview();
        }
      }
    });
  }

  if (skuInput) {
    skuInput.addEventListener('input', updatePreview);
  }

  if (descriptionInput) {
    descriptionInput.addEventListener('input', updatePreview);
  }

  // Reset form when modal is closed
  if (modal) {
    modal.addEventListener('hidden.bs.modal', function() {
      const form = document.getElementById('createBundleForm');
      if (form) {
        form.reset();
        form.classList.remove('was-validated');
        updatePreview();
      }
    });
  }

  // Form validation
  const form = document.getElementById('createBundleForm');
  if (form) {
    form.addEventListener('submit', function(event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    });
  }

  // Handle delete confirmations with better UX
  const deleteLinks = document.querySelectorAll('a[data-turbo-method="delete"]');
  
  deleteLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const bundleName = this.closest('tr').querySelector('strong').textContent;
      const confirmMessage = `Are you sure you want to delete "${bundleName}"?\n\nThis action cannot be undone and will remove all associated categories and products.`;
      
      if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
      }
    });
  });
  
  // Handle status toggle confirmations
  const statusLinks = document.querySelectorAll('a[data-turbo-method="patch"]');
  
  statusLinks.forEach(link => {
    if (link.textContent.includes('Deactivate')) {
      link.addEventListener('click', function(e) {
        const bundleName = this.closest('tr').querySelector('strong').textContent;
        const confirmMessage = `Are you sure you want to deactivate "${bundleName}"?\n\nThis will make the bundle unavailable for customers.`;
        
        if (!confirm(confirmMessage)) {
          e.preventDefault();
          return false;
        }
      });
    }
  });
});
</script>

<!-- Bundle Builder Modal - Almost Full Screen -->
<div class="modal fade" id="bundleBuilderModal" tabindex="-1" aria-labelledby="bundleBuilderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-fullscreen-custom">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h6 class="modal-title mb-0" id="bundleBuilderModalLabel">
          <span class="me-2">🏗️</span>Bundle Builder
          <small class="text-muted ms-2" id="bundleBuilderBundleName"></small>
        </h6>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-0" id="bundleBuilderContent">
        <!-- Bundle Builder content will be loaded here -->
        <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Bundle Configuration Complete Modal -->
<div class="modal fade" id="bundleCompleteModal" tabindex="-1" aria-labelledby="bundleCompleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title" id="bundleCompleteModalLabel">
          <span class="me-2">🎉</span>Bundle Configuration Complete!
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="text-center mb-4">
          <div class="display-1 mb-3">📦</div>
          <h4 id="completeBundleName">Bundle Name</h4>
          <p class="text-muted">Your bundle has been successfully configured!</p>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card border-0 bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Categories</h6>
                <div class="display-6 text-primary" id="completeCategoriesCount">0</div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card border-0 bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Products</h6>
                <div class="display-6 text-success" id="completeProductsCount">0</div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h6>Next Steps:</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <span class="badge bg-primary me-2">1</span>
              Review and test your bundle configuration
            </li>
            <li class="mb-2">
              <span class="badge bg-primary me-2">2</span>
              Activate the bundle when ready
            </li>
            <li class="mb-2">
              <span class="badge bg-primary me-2">3</span>
              Monitor customer interactions and feedback
            </li>
          </ul>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" id="backToBuilderBtn">
          <span class="me-1">←</span>Back to Builder
        </button>
        <button type="button" class="btn btn-outline-primary" id="activateBundleBtn">
          <span class="me-1">🚀</span>Activate Bundle
        </button>
        <button type="button" class="btn btn-primary" onclick="window.location.reload()">
          <span class="me-1">📋</span>Back to Bundles
        </button>
      </div>
    </div>
  </div>
</div>

<style>
/* Custom Modal Almost Full Screen */
.modal-fullscreen-custom {
  width: 95vw !important;
  height: 75vh !important;
  max-width: none !important;
  margin: 5vh auto !important;
}

.modal-fullscreen-custom .modal-content {
  height: 100% !important;
  border-radius: 12px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-fullscreen-custom .modal-body {
  height: 100% !important; /* Use full height since no header */
  overflow: hidden !important;
  padding: 0 !important;
}

/* Builder icon styling */
.action-icon.builder-icon {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.action-icon.builder-icon:hover {
  background: #e3f2fd;
  transform: scale(1.1);
}

/* Bundle Complete Modal Styling */
#bundleCompleteModal .modal-header {
  background: linear-gradient(135deg, #28a745, #20c997);
}

#bundleCompleteModal .display-1 {
  font-size: 4rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.card.border-0.bg-light {
  transition: transform 0.2s ease;
}

.card.border-0.bg-light:hover {
  transform: translateY(-2px);
}

/* Bundle Builder Styles for Modal */
.bundle-builder-container {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.builder-header-minimal {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 6px 15px !important;
  flex-shrink: 0;
  min-height: 35px !important;
}

.builder-main-container {
  display: flex !important;
  flex: 1 !important;
  overflow: hidden !important;
  height: calc(100% - 35px) !important;
}

.builder-left-half {
  width: 50% !important;
  display: flex !important;
  flex-direction: column !important;
  border-right: 1px solid #e9ecef;
}

.builder-right-half {
  width: 50% !important;
  display: flex !important;
  flex-direction: column !important;
}

.categories-section {
  height: 40% !important;
  padding: 8px !important;
  border-bottom: 1px solid #e9ecef;
  overflow-y: auto;
}

.products-section {
  height: 60% !important;
  padding: 8px !important;
  overflow-y: auto;
}

.section-header {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.store-category-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.store-category-card:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.store-category-card.active {
  background: #e3f2fd;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.category-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

.category-name {
  font-size: 11px;
  font-weight: 600;
  color: #495057;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.store-product-card {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  cursor: move;
  transition: all 0.2s ease;
}

.store-product-card:hover {
  background: #f8f9fa;
  border-color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-emoji {
  font-size: 16px;
  margin-bottom: 4px;
}

.product-name {
  font-size: 10px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 2px;
}

.product-price {
  font-size: 9px;
  color: #6c757d;
}

.bundle-drop-zone-full {
  flex: 1 !important;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  padding: 8px !important;
  margin: 8px !important;
  background: #f8f9fa;
  transition: all 0.3s ease;
  overflow-y: auto;
  position: relative;
}

.bundle-drop-zone-full.drag-over {
  border-color: #28a745;
  background: #d4edda;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 120px;
  text-align: center;
}

.drop-zone-icon {
  font-size: 48px;
  opacity: 0.5;
}

.drop-zone-text {
  font-size: 16px;
  font-weight: 600;
  color: #6c757d;
}

.selected-products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  padding: 10px;
}

.bundle-actions {
  padding: 6px 15px !important;
  background: white;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
  min-height: 40px !important;
}

.bundle-info {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>

<script>
// Global variables
let currentBundleId = null;

// Header Management Functions
function updateContextualHeader(title, actions = []) {
  const titleElement = document.getElementById('contextual-title');
  const actionsContainer = document.getElementById('header-actions');

  if (titleElement) {
    titleElement.textContent = title;
  }

  if (actionsContainer) {
    actionsContainer.innerHTML = actions.map(action =>
      `<button class="btn ${action.class}" onclick="${action.onclick}">${action.text}</button>`
    ).join('');
  }
}

function resetHeaderToDefault() {
  updateContextualHeader('Dynamic Bundle Admin', []);
}

function closeBundleBuilder() {
  const builderModal = document.getElementById('bundleBuilderModal');
  const builderModalInstance = bootstrap.Modal.getInstance(builderModal);
  if (builderModalInstance) {
    builderModalInstance.hide();
  }
  resetHeaderToDefault();
}

// Bundle Builder Modal Handler
document.addEventListener('DOMContentLoaded', function() {
  const builderModal = document.getElementById('bundleBuilderModal');
  const completeModal = document.getElementById('bundleCompleteModal');
  const builderButtons = document.querySelectorAll('[data-bs-target="#bundleBuilderModal"]');

  builderButtons.forEach(button => {
    button.addEventListener('click', function() {
      const bundleId = this.dataset.bundleId;
      const bundleName = this.dataset.bundleName;

      // Store current bundle ID globally
      currentBundleId = bundleId;

      // Update contextual header with actions
      updateContextualHeader(`🏗️ Bundle Builder - ${bundleName}`, [
        {
          text: '✅ Complete & Save',
          class: 'btn-success',
          onclick: `completeBundleConfiguration('${bundleId}', '${bundleName}')`
        },
        {
          text: 'Cancel',
          class: 'btn-outline-secondary',
          onclick: 'closeBundleBuilder()'
        }
      ]);

      // Load builder content
      loadBundleBuilder(bundleId, bundleName);
    });
  });

  function loadBundleBuilder(bundleId, bundleName) {
    const contentDiv = document.getElementById('bundleBuilderContent');

    // Show loading spinner
    contentDiv.innerHTML = `
      <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading Bundle Builder...</span>
        </div>
      </div>
    `;

    // For now, load the existing builder content (we'll create the endpoint later)
    // Simulate loading the builder interface
    setTimeout(() => {
      contentDiv.innerHTML = createBuilderInterface(bundleId, bundleName);
      initializeBundleBuilderInModal();
    }, 1000);
  }

  function createBuilderInterface(bundleId, bundleName) {
    return `
      <div class="bundle-builder-container" style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
        <!-- Minimal Header -->
        <div class="builder-header-minimal" style="padding: 6px 15px; min-height: 35px; background: white; border-bottom: 1px solid #e9ecef; flex-shrink: 0;">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h6 class="mb-0">
                <span class="me-2">🏗️</span>Building: <strong>${bundleName}</strong>
              </h6>
            </div>
            <div>
              <button type="button" class="btn btn-success btn-sm" onclick="completeBundleConfiguration('${bundleId}', '${bundleName}')">
                <span class="me-1">✅</span>Complete Configuration
              </button>
            </div>
          </div>
        </div>

        <!-- Builder Interface -->
        <div class="builder-main-container" style="display: flex; flex: 1; overflow: hidden; height: calc(100% - 35px);">
          <!-- Left Half - Store Catalog -->
          <div class="builder-left-half" style="width: 50%; display: flex; flex-direction: column; border-right: 1px solid #e9ecef;">
            <div class="categories-section" style="height: 40%; padding: 8px; border-bottom: 1px solid #e9ecef; overflow-y: auto;">
              <div class="section-header" style="margin-bottom: 8px; padding-bottom: 6px; border-bottom: 1px solid #f0f0f0;">
                <h6 style="margin: 0; font-size: 14px; font-weight: 600;"><span class="me-2">🏪</span>Store Categories</h6>
              </div>
              <div class="store-categories-container">
                <div class="categories-grid">
                  <div class="store-category-card active" data-category="supplements">
                    <div class="category-icon">💊</div>
                    <div class="category-name">Supplements</div>
                  </div>
                  <div class="store-category-card" data-category="nutrition">
                    <div class="category-icon">🥗</div>
                    <div class="category-name">Nutrition</div>
                  </div>
                  <div class="store-category-card" data-category="fitness">
                    <div class="category-icon">💪</div>
                    <div class="category-name">Fitness</div>
                  </div>
                  <div class="store-category-card" data-category="wellness">
                    <div class="category-icon">🧘</div>
                    <div class="category-name">Wellness</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="products-section" style="height: 60%; padding: 8px; overflow-y: auto;">
              <div class="section-header" style="margin-bottom: 8px; padding-bottom: 6px; border-bottom: 1px solid #f0f0f0;">
                <h6 style="margin: 0; font-size: 14px; font-weight: 600;"><span class="me-2">📦</span>Products <small class="text-muted">Supplements</small></h6>
              </div>
              <div class="store-products-container">
                <div class="products-grid" id="modalProductsGrid">
                  <!-- Products will be loaded here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Right Half - Bundle Drop Zone -->
          <div class="builder-right-half" style="width: 50%; display: flex; flex-direction: column;">
            <div class="section-header" style="padding: 8px 15px; margin-bottom: 8px; padding-bottom: 6px; border-bottom: 1px solid #f0f0f0;">
              <h6 style="margin: 0; font-size: 14px; font-weight: 600;"><span class="me-2">📦</span>Bundle Products <small class="text-muted">(Drop products here)</small></h6>
            </div>

            <div class="bundle-drop-zone-full" id="modalBundleDropZone" style="flex: 1; border: 2px dashed #dee2e6; border-radius: 12px; padding: 8px; margin: 8px; background: #f8f9fa; overflow-y: auto; position: relative;">
              <div class="drop-zone-empty" id="modalDropZoneEmpty" style="display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 6px; height: 120px; text-align: center;">
                <div class="drop-zone-icon" style="font-size: 48px; opacity: 0.5;">📦</div>
                <div class="drop-zone-text" style="font-size: 16px; font-weight: 600; color: #6c757d;">Drop products here to add to bundle</div>
                <small class="text-muted">Drag products from the left panel</small>
              </div>
              <div class="selected-products-grid" id="modalSelectedProductsGrid"></div>
            </div>

            <div class="bundle-actions" style="padding: 6px 15px; background: white; border-top: 1px solid #e9ecef; flex-shrink: 0; min-height: 40px;">
              <div class="d-flex justify-content-between align-items-center">
                <div class="bundle-info">
                  <span class="text-muted">Products: <span class="badge bg-primary" id="modalProductsCount">0</span></span>
                </div>
                <button type="button" class="btn btn-success" onclick="completeBundleConfiguration('${bundleId}', '${bundleName}')">
                  <span class="me-1">✅</span>Complete & Save
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  function initializeBundleBuilderInModal() {
    // Initialize the builder functionality within the modal
    // This would include drag & drop, product loading, etc.
    console.log('Bundle Builder initialized in modal');

    // Load initial products
    loadModalProducts('supplements');
  }

  function loadModalProducts(category) {
    const productsGrid = document.getElementById('modalProductsGrid');
    const mockProducts = {
      supplements: [
        { id: 1, name: 'Vitamin D3', price: 24.99, emoji: '☀️' },
        { id: 2, name: 'Omega-3', price: 32.99, emoji: '🐟' },
        { id: 3, name: 'Probiotics', price: 28.99, emoji: '🦠' },
        { id: 4, name: 'Magnesium', price: 19.99, emoji: '⚡' }
      ]
    };

    const products = mockProducts[category] || [];
    productsGrid.innerHTML = products.map(product => `
      <div class="store-product-card" draggable="true" data-product='${JSON.stringify(product)}'>
        <div class="product-emoji">${product.emoji}</div>
        <div class="product-name">${product.name}</div>
        <div class="product-price">$${product.price}</div>
      </div>
    `).join('');
  }

  // Global function to complete bundle configuration
  window.completeBundleConfiguration = function(bundleId, bundleName) {
    // Hide builder modal
    const builderModalInstance = bootstrap.Modal.getInstance(builderModal);
    builderModalInstance.hide();

    // Update header for configuration complete
    updateContextualHeader(`⚙️ Bundle Configuration - ${bundleName}`, [
      {
        text: '🚀 Activate Bundle',
        class: 'btn-success',
        onclick: `activateBundle('${bundleId}', '${bundleName}')`
      },
      {
        text: '← Back to Builder',
        class: 'btn-outline-secondary',
        onclick: 'backToBundleBuilder()'
      },
      {
        text: '📋 Back to Bundles',
        class: 'btn-outline-primary',
        onclick: 'backToBundlesList()'
      }
    ]);

    // Show completion modal
    document.getElementById('completeBundleName').textContent = bundleName;
    document.getElementById('completeCategoriesCount').textContent = '3'; // Mock data
    document.getElementById('completeProductsCount').textContent = '8'; // Mock data

    const completeModalInstance = new bootstrap.Modal(completeModal);
    completeModalInstance.show();
  };

  // Global functions for header actions
  window.backToBundleBuilder = function() {
    // Hide complete modal
    const completeModalInstance = bootstrap.Modal.getInstance(completeModal);
    completeModalInstance.hide();

    // Update header back to builder
    const bundleName = document.getElementById('completeBundleName').textContent;
    updateContextualHeader(`🏗️ Bundle Builder - ${bundleName}`, [
      {
        text: '✅ Complete & Save',
        class: 'btn-success',
        onclick: `completeBundleConfiguration('${currentBundleId}', '${bundleName}')`
      },
      {
        text: 'Cancel',
        class: 'btn-outline-secondary',
        onclick: 'closeBundleBuilder()'
      }
    ]);

    // Show builder modal again
    const builderModalInstance = new bootstrap.Modal(builderModal);
    builderModalInstance.show();
  };

  window.backToBundlesList = function() {
    // Hide complete modal
    const completeModalInstance = bootstrap.Modal.getInstance(completeModal);
    completeModalInstance.hide();

    // Reset header
    resetHeaderToDefault();

    // Reload page to show bundles list
    window.location.reload();
  };

  // Handle back to builder button (legacy)
  document.getElementById('backToBuilderBtn').addEventListener('click', function() {
    backToBundleBuilder();
  });

  // Global function to activate bundle
  window.activateBundle = function(bundleId, bundleName) {
    // Update header to show activation in progress
    updateContextualHeader(`🚀 Activating Bundle - ${bundleName}`, []);

    // Simulate activation process
    setTimeout(() => {
      // Update header to show success
      updateContextualHeader(`✅ Bundle Activated - ${bundleName}`, [
        {
          text: '📋 Back to Bundles',
          class: 'btn-primary',
          onclick: 'backToBundlesList()'
        }
      ]);

      // Auto-close and reload after success
      setTimeout(() => {
        const completeModalInstance = bootstrap.Modal.getInstance(completeModal);
        completeModalInstance.hide();
        resetHeaderToDefault();
        window.location.reload();
      }, 2000);
    }, 2000);
  };

  // Handle activate bundle button (legacy)
  document.getElementById('activateBundleBtn').addEventListener('click', function() {
    const bundleName = document.getElementById('completeBundleName').textContent;
    activateBundle(currentBundleId, bundleName);
  });

  // Reset header when modals are closed
  builderModal.addEventListener('hidden.bs.modal', function() {
    resetHeaderToDefault();
  });

  completeModal.addEventListener('hidden.bs.modal', function() {
    resetHeaderToDefault();
  });

  // Make functions globally available
  window.loadBundleBuilder = loadBundleBuilder;
});
</script>

<!-- Bundle Builder Modal - Almost Full Screen -->
<div class="modal fade" id="bundleBuilderModal" tabindex="-1" aria-labelledby="bundleBuilderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-fullscreen-custom">
    <div class="modal-content">
      <div class="modal-body p-0" id="bundleBuilderContent">
        <!-- Bundle Builder content will be loaded here -->
        <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Custom Modal Almost Full Screen */
.modal-fullscreen-custom {
  width: 95vw;
  height: 90vh;
  max-width: none;
  margin: 2.5vh auto;
}

.modal-fullscreen-custom .modal-content {
  height: 100%;
  border-radius: 12px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-fullscreen-custom .modal-body {
  height: calc(100% - 60px); /* Subtract header height */
  overflow: hidden;
}

/* Builder icon styling */
.action-icon.builder-icon {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.action-icon.builder-icon:hover {
  background: #e3f2fd;
  transform: scale(1.1);
}
</style>

<script>
// Bundle Builder Modal Handler
document.addEventListener('DOMContentLoaded', function() {
  const builderModal = document.getElementById('bundleBuilderModal');
  const builderButtons = document.querySelectorAll('[data-bs-target="#bundleBuilderModal"]');

  builderButtons.forEach(button => {
    button.addEventListener('click', function() {
      const bundleId = this.dataset.bundleId;
      const bundleName = this.dataset.bundleName;

      // Update modal title
      document.getElementById('bundleBuilderBundleName').textContent = bundleName;

      // Load builder content
      loadBundleBuilder(bundleId, bundleName);
    });
  });

  function loadBundleBuilder(bundleId, bundleName) {
    const contentDiv = document.getElementById('bundleBuilderContent');

    // Show loading spinner
    contentDiv.innerHTML = `
      <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading Bundle Builder...</span>
        </div>
      </div>
    `;

    // Fetch builder content (we'll create this endpoint)
    fetch(`/admin/bundles/${bundleId}/builder_modal`)
      .then(response => response.text())
      .then(html => {
        contentDiv.innerHTML = html;
        // Initialize builder functionality
        if (window.initializeBundleBuilder) {
          window.initializeBundleBuilder();
        }
      })
      .catch(error => {
        console.error('Error loading bundle builder:', error);
        contentDiv.innerHTML = `
          <div class="alert alert-danger m-4">
            <h5>Error Loading Builder</h5>
            <p>Unable to load the Bundle Builder. Please try again.</p>
            <button type="button" class="btn btn-outline-danger" onclick="loadBundleBuilder('${bundleId}', '${bundleName}')">
              Retry
            </button>
          </div>
        `;
      });
  }

  // Make loadBundleBuilder globally available for retry button
  window.loadBundleBuilder = loadBundleBuilder;
});
</script>
