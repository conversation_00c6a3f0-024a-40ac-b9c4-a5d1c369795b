# frozen_string_literal: true

# Bundle Wizard State Machine
# Manages the step-by-step process of creating a dynamic bundle
class BundleWizard
  include ActiveModel::Model
  include ActiveModel::Attributes
  include AASM

  # Attributes
  attribute :session_id, :string
  attribute :name, :string
  attribute :sku, :string
  attribute :description, :string
  attribute :created_at, :datetime, default: -> { Time.current }
  attribute :bundle_id, :string  # Fluid bundle ID after creation
  attribute :state, :string, default: 'draft'  # AASM state column

  # Arrays need to be handled differently in ActiveModel
  attr_accessor :categories, :products

  def initialize(attributes = {})
    super
    @categories = attributes[:categories] || []
    @products = attributes[:products] || []
    @state = attributes[:state] || 'draft'
  end

  # Validations
  validates :name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :sku, presence: true, length: { minimum: 2, maximum: 50 }
  validates :description, length: { maximum: 500 }

  # State Machine
  aasm column: :state, whiny_transitions: false do
    # States
    state :draft, initial: true
    state :info_complete
    state :builder_complete
    state :ready_to_save
    state :completed
    state :failed

    # Events (Transitions)
    event :complete_info do
      transitions from: :draft, to: :info_complete
      # TEMPORARILY REMOVED: guard: :info_valid?
    end

    event :complete_builder do
      transitions from: :info_complete, to: :builder_complete, guard: :builder_valid?
    end

    event :ready_for_preview do
      transitions from: :builder_complete, to: :ready_to_save
    end

    event :complete_wizard do
      transitions from: :ready_to_save, to: :completed, after: :create_bundle_in_fluid
    end

    event :fail_wizard do
      transitions from: [:draft, :info_complete, :builder_complete, :ready_to_save], to: :failed
    end

    # Allow going back to previous steps
    event :back_to_info do
      transitions from: [:info_complete, :builder_complete, :ready_to_save], to: :draft
    end

    event :back_to_builder do
      transitions from: [:builder_complete, :ready_to_save], to: :info_complete
    end

    event :back_to_preview do
      transitions from: :ready_to_save, to: :builder_complete
    end
  end

  # State helpers
  def current_step
    case aasm.current_state.to_s
    when 'draft'
      'info'
    when 'info_complete'
      'builder'
    when 'builder_complete', 'ready_to_save'
      'preview'
    when 'completed'
      'completed'
    when 'failed'
      'error'
    else
      'info'
    end
  end

  def step_number
    case aasm.current_state.to_s
    when 'draft'
      1
    when 'info_complete'
      2
    when 'builder_complete', 'ready_to_save'
      3
    when 'completed'
      4
    else
      1
    end
  end

  def can_proceed_to_step?(step)
    case step
    when 'info'
      true  # Always can go to step 1
    when 'builder'
      info_complete? || builder_complete? || ready_to_save?
    when 'preview'
      builder_complete? || ready_to_save?
    else
      false
    end
  end

  def progress_percentage
    case aasm.current_state.to_s
    when 'draft'
      0
    when 'info_complete'
      33
    when 'builder_complete'
      66
    when 'ready_to_save'
      90
    when 'completed'
      100
    else
      0
    end
  end

  # Session management
  def self.from_session(session)
    data = session[:bundle_wizard] || {}
    Rails.logger.info("WIZARD SESSION: Loading wizard from session: #{data}")

    wizard = new(data)
    wizard.session_id = session.id.to_s if session.respond_to?(:id)

    # IMPORTANT: Handle arrays FIRST before state transitions
    # because builder_valid? guard depends on products/categories
    wizard.categories = data[:categories] || data['categories'] || []
    wizard.products = data[:products] || data['products'] || []
    Rails.logger.info("WIZARD SESSION: Assigned products: #{wizard.products.length}, categories: #{wizard.categories.length}")

    # Restore AASM state by replaying transitions
    saved_state = data[:state] || data['state'] || 'draft'
    Rails.logger.info("WIZARD SESSION: Restoring state: #{saved_state} (type: #{saved_state.class})")

    # Replay transitions to reach the saved state
    case saved_state.to_s
    when 'info_complete'
      Rails.logger.info("WIZARD SESSION: Replaying complete_info transition")
      wizard.complete_info if wizard.may_complete_info?
    when 'builder_complete'
      Rails.logger.info("WIZARD SESSION: Replaying complete_info and complete_builder transitions")
      wizard.complete_info if wizard.may_complete_info?
      Rails.logger.info("WIZARD SESSION: builder_valid? = #{wizard.send(:builder_valid?)}")
      wizard.complete_builder if wizard.may_complete_builder?
    when 'ready_to_save'
      Rails.logger.info("WIZARD SESSION: Replaying transitions to ready_to_save")
      wizard.complete_info if wizard.may_complete_info?
      wizard.complete_builder if wizard.may_complete_builder?
      wizard.ready_for_preview if wizard.may_ready_for_preview?
    when 'completed'
      Rails.logger.info("WIZARD SESSION: Replaying all transitions to completed")
      wizard.complete_info if wizard.may_complete_info?
      wizard.complete_builder if wizard.may_complete_builder?
      wizard.ready_for_preview if wizard.may_ready_for_preview?
      wizard.complete_wizard if wizard.may_complete_wizard?
    else
      Rails.logger.info("WIZARD SESSION: Keeping default draft state")
    end

    Rails.logger.info("WIZARD SESSION: Final state after restoration: #{wizard.aasm.current_state}")

    wizard
  end

  def save_to_session(session)
    current_state = aasm.current_state.to_s
    Rails.logger.info("WIZARD SESSION: Saving wizard with state: #{current_state}")

    session[:bundle_wizard] = {
      state: current_state,
      name: name,
      sku: sku,
      description: description,
      categories: categories,
      products: products,
      created_at: created_at,
      bundle_id: bundle_id
    }

    Rails.logger.info("WIZARD SESSION: Session data saved: #{session[:bundle_wizard]}")
  end

  def clear_from_session(session)
    session.delete(:bundle_wizard)
  end

  # Data management
  def update_info(params)
    Rails.logger.info("WIZARD UPDATE: Starting update_info with state: #{aasm.current_state}")

    self.name = params[:name]
    self.sku = params[:sku]
    self.description = params[:description]

    # Auto-generate SKU if not provided but name is present
    if self.sku.blank? && self.name.present?
      self.sku = generate_sku_from_name(self.name)
      Rails.logger.info("WIZARD UPDATE: Auto-generated SKU: #{self.sku}")
    end

    Rails.logger.info("WIZARD UPDATE: Info valid? #{info_valid?}")

    if info_valid?
      Rails.logger.info("WIZARD UPDATE: Calling complete_info transition")
      result = complete_info
      Rails.logger.info("WIZARD UPDATE: Transition result: #{result}, new state: #{aasm.current_state}")
      true
    else
      Rails.logger.warn("WIZARD UPDATE: Info validation failed")
      false
    end
  end

  def update_builder_config(config)
    self.categories = config[:categories] || []
    self.products = config[:products] || []
    
    if builder_valid?
      complete_builder
      ready_for_preview
      true
    else
      false
    end
  end

  # Summary data
  def summary
    {
      name: name,
      sku: sku,
      description: description,
      categories_count: categories.length,
      products_count: products.length,
      state: aasm.current_state.to_s,
      step: current_step,
      progress: progress_percentage
    }
  end

  # Generate SKU from name (same logic as Bundle model)
  def generate_sku_from_name(name)
    return '' if name.blank?

    base_sku = name
      .upcase
      .gsub(/[^A-Z0-9\s]/, '')
      .gsub(/\s+/, '-')
      .slice(0, 17) # Leave room for -001 suffix

    "#{base_sku}-001"
  end

  private

  # Guards
  def info_valid?
    result = name.present? && sku.present? && valid?
    Rails.logger.info("WIZARD VALIDATION: info_valid? = #{result} (name: #{name.present?}, sku: #{sku.present?}, valid: #{valid?})")
    if !valid?
      Rails.logger.warn("WIZARD VALIDATION: Validation errors: #{errors.full_messages}")
    end
    result
  end

  def builder_valid?
    # At least one product or category configured
    products.any? || categories.any?
  end

  # Callbacks
  def create_bundle_in_fluid
    # This will be called when transitioning to :completed
    Rails.logger.info("WIZARD STATE MACHINE: Creating bundle in Fluid for #{name}")
    
    # Here we would call the Fluid API
    # For now, just set a mock bundle_id
    self.bundle_id = "bundle_#{SecureRandom.hex(8)}"
    
    Rails.logger.info("WIZARD STATE MACHINE: Bundle created with ID: #{bundle_id}")
  end
end
