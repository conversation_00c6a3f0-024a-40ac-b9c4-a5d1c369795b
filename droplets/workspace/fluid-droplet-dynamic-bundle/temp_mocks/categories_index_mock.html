<!-- NUESTRO MOCK ORIGINAL - Categories Index Page -->
<!-- Extraído del branch 69 commit 24c396c -->

<% content_for :title, "Categories - #{@bundle['name']}" %>

<div class="d-flex justify-content-between align-items-center mb-4">
  <div>
    <h2>Categories for "<%= @bundle['name'] %>"</h2>
    <p class="text-muted mb-0">
      SKU: <code><%= @bundle['sku'] %></code> • 
      <%= @categories.length %> categories configured
    </p>
  </div>
  
  <div class="header-actions">
    <%= link_to admin_bundle_path(@bundle_id), class: "btn btn-secondary" do %>
      ← Back to Bundle
    <% end %>
    <%= link_to new_admin_bundle_category_path(@bundle_id), class: "btn btn-primary" do %>
      🏷️ Add Category
    <% end %>
  </div>
</div>

<% if @categories.any? %>
  <div class="categories-grid">
    <% @categories.sort_by { |cat| cat['displayOrder'] || 0 }.each_with_index do |category, index| %>
      <div class="category-card" data-category-id="<%= category['categoryId'] %>">
        <div class="category-header">
          <div class="category-info">
            <h3 class="category-name"><%= category['categoryName'] %></h3>
            <div class="category-meta">
              <span class="selection-info">
                Select <%= category['selectionQuantity'] %> 
                <%= category['selectionQuantity'] == 1 ? 'item' : 'items' %>
              </span>
              <span class="products-count">
                <%= category['products']&.length || 0 %> products
              </span>
            </div>
          </div>
          
          <div class="category-actions">
            <div class="order-controls">
              <% unless index == 0 %>
                <%= link_to move_up_admin_bundle_category_path(@bundle_id, category['categoryId']), 
                    method: :patch,
                    class: "order-btn",
                    title: "Move up",
                    data: { turbo_method: :patch } do %>
                  ⬆️
                <% end %>
              <% end %>
              
              <% unless index == @categories.length - 1 %>
                <%= link_to move_down_admin_bundle_category_path(@bundle_id, category['categoryId']), 
                    method: :patch,
                    class: "order-btn",
                    title: "Move down",
                    data: { turbo_method: :patch } do %>
                  ⬇️
                <% end %>
              <% end %>
            </div>
            
            <div class="action-icons">
              <%= link_to edit_admin_bundle_category_path(@bundle_id, category['categoryId']), 
                  class: "action-icon edit-icon",
                  title: "Edit category" do %>
                ✏️
              <% end %>
              
              <%= link_to admin_bundle_category_path(@bundle_id, category['categoryId']), 
                  method: :delete,
                  class: "action-icon delete-icon",
                  title: "Delete category",
                  data: { 
                    confirm: "Are you sure you want to delete '#{category['categoryName']}'? This will remove all assigned products.",
                    turbo_method: :delete
                  } do %>
                🗑️
              <% end %>
            </div>
          </div>
        </div>
        
        <div class="category-body">
          <% if category['products']&.any? %>
            <div class="products-preview">
              <h4>Products in this category:</h4>
              <div class="products-list">
                <% category['products'].first(3).each do |product| %>
                  <div class="product-item">
                    <span class="product-name"><%= product['variantTitle'] %></span>
                    <% if product['isDefault'] %>
                      <span class="default-badge">Default</span>
                    <% end %>
                  </div>
                <% end %>
                
                <% if category['products'].length > 3 %>
                  <div class="more-products">
                    +<%= category['products'].length - 3 %> more products
                  </div>
                <% end %>
              </div>
            </div>
          <% else %>
            <div class="empty-products">
              <div class="empty-icon">📦</div>
              <p>No products assigned yet</p>
              <button class="btn btn-sm btn-outline-primary" onclick="assignProducts('<%= category['categoryId'] %>')">
                Assign Products
              </button>
            </div>
          <% end %>
        </div>
        
        <div class="category-footer">
          <div class="category-stats">
            <span class="stat">
              <strong>Order:</strong> <%= category['displayOrder'] + 1 %>
            </span>
            <span class="stat">
              <strong>Required:</strong> <%= category['selectionQuantity'] %>
            </span>
            <span class="stat">
              <strong>Available:</strong> <%= category['products']&.length || 0 %>
            </span>
          </div>
        </div>
      </div>
    <% end %>
  </div>

<% else %>
  <!-- Empty State -->
  <div class="empty-state">
    <div class="empty-icon">🏷️</div>
    <h3>No Categories Yet</h3>
    <p class="text-muted mb-4">
      Categories help organize products within your bundle. Customers will select items from each category.
    </p>
    
    <div class="example-categories">
      <h4>Example Categories:</h4>
      <div class="example-grid">
        <div class="example-item">
          <strong>🥤 Protein Powders</strong>
          <small>Select 2 items</small>
        </div>
        <div class="example-item">
          <strong>⚡ Pre-Workout</strong>
          <small>Select 1 item</small>
        </div>
        <div class="example-item">
          <strong>🏃 Recovery</strong>
          <small>Select 1 item</small>
        </div>
      </div>
    </div>
    
    <%= link_to new_admin_bundle_category_path(@bundle_id), class: "btn btn-primary btn-lg" do %>
      🏷️ Create Your First Category
    <% end %>
  </div>
<% end %>

<!-- ESTILOS CSS INCLUIDOS EN EL MOCK ORIGINAL -->
<style>
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 24px;
  }

  .category-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .category-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .category-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .category-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #495057;
  }

  .category-meta {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #6c757d;
  }

  .selection-info {
    font-weight: 500;
  }

  .category-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .order-controls {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .order-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: #e9ecef;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
  }

  .order-btn:hover {
    background: #007bff;
    transform: scale(1.1);
  }

  .action-icons {
    display: flex;
    gap: 8px;
  }

  .action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .edit-icon {
    background-color: #e3f2fd;
    border-color: #bbdefb;
  }

  .edit-icon:hover {
    background-color: #bbdefb;
    border-color: #2196f3;
    transform: translateY(-1px);
  }

  .delete-icon {
    background-color: #ffebee;
    border-color: #ffcdd2;
  }

  .delete-icon:hover {
    background-color: #ffcdd2;
    border-color: #f44336;
    transform: translateY(-1px);
  }

  .category-body {
    padding: 20px;
  }

  .products-preview h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #495057;
    font-weight: 600;
  }

  .products-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
  }

  .product-name {
    color: #495057;
  }

  .default-badge {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
  }

  .more-products {
    padding: 8px 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
  }

  .empty-products {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
  }

  .empty-products .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .category-footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
  }

  .category-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
  }

  .empty-state {
    text-align: center;
    padding: 80px 20px;
  }

  .empty-state .empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
  }

  .example-categories {
    margin: 32px 0;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .example-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
  }

  .example-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }
</style>
